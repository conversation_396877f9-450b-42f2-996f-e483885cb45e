import { defineStore } from 'pinia'
import modalApi from '@/api/modal'

export const useAirtightTestChartStore = defineStore('airtightTestChart', {
  state: () => ({
    // 查询表单状态
    searchForm: {
      vehicleModelId: null,
      dateRange: []
    },

    // 选项数据
    vehicleModelOptions: [],

    // 加载状态
    vehicleModelsLoading: false,
    queryLoading: false,

    // 查询结果
    testDataList: [],

    // 分页状态
    pagination: {
      currentPage: 1,
      pageSize: 10,
      total: 0
    },

    // 图表状态
    chartInstance: null,
    chartInitialized: false
  }),
  
  getters: {
    // 是否可以查询
    canQuery: (state) => {
      return state.searchForm.vehicleModelId
    },

    // 是否有查询结果
    hasResults: (state) => {
      return state.testDataList.length > 0
    },

    // 分页信息
    paginationInfo: (state) => {
      const start = (state.pagination.currentPage - 1) * state.pagination.pageSize + 1
      const end = Math.min(state.pagination.currentPage * state.pagination.pageSize, state.pagination.total)
      return {
        start,
        end,
        total: state.pagination.total
      }
    }
  },
  
  actions: {
    // 加载车型列表
    async loadVehicleModels() {
      try {
        this.vehicleModelsLoading = true
        const response = await modalApi.getVehicleModels()
        this.vehicleModelOptions = response.data || []
      } catch (error) {
        console.error('加载车型列表失败:', error)
        throw error
      } finally {
        this.vehicleModelsLoading = false
      }
    },

    // 查询气密性测试数据
    async queryTestData(page = 1) {
      if (!this.canQuery) {
        throw new Error('请选择车型')
      }

      try {
        this.queryLoading = true

        const params = {
          vehicle_model_ids: this.searchForm.vehicleModelId.toString(),
          page: page,
          page_size: this.pagination.pageSize
        }

        // 添加日期范围参数
        if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {
          params.start_date = this.searchForm.dateRange[0]
          params.end_date = this.searchForm.dateRange[1]
        }

        // 使用气密性对比API获取数据
        const response = await modalApi.compareAirtightnessData(params)
        this.testDataList = response.data?.leakage_data || []
        this.pagination.total = this.testDataList.length
        this.pagination.currentPage = page

        return this.testDataList
      } catch (error) {
        console.error('查询气密性测试数据失败:', error)
        throw error
      } finally {
        this.queryLoading = false
      }
    },
    
    // 设置车型
    setVehicleModel(vehicleModelId) {
      this.searchForm.vehicleModelId = vehicleModelId
      // 清空结果
      this.testDataList = []
      this.pagination.currentPage = 1
      this.pagination.total = 0
      this.chartInitialized = false
    },

    // 设置日期范围
    setDateRange(dateRange) {
      this.searchForm.dateRange = dateRange
      // 清空结果
      this.testDataList = []
      this.pagination.currentPage = 1
      this.pagination.total = 0
      this.chartInitialized = false
    },

    // 切换页码
    changePage(page) {
      this.queryTestData(page)
    },

    // 改变页面大小
    changePageSize(pageSize) {
      this.pagination.pageSize = pageSize
      this.pagination.currentPage = 1
      this.queryTestData(1)
    },
    
    // 设置图表实例
    setChartInstance(instance) {
      this.chartInstance = instance
      this.chartInitialized = !!instance
    },

    // 清空所有状态
    resetState() {
      this.searchForm = {
        vehicleModelId: null,
        dateRange: []
      }
      this.testDataList = []
      this.pagination = {
        currentPage: 1,
        pageSize: 10,
        total: 0
      }
      this.chartInitialized = false
      this.chartInstance = null
    },
    
    // 初始化页面数据
    async initializePageData() {
      if (this.vehicleModelOptions.length === 0) {
        await this.loadVehicleModels()
      }
    }
  }
})
