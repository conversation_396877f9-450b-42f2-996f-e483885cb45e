import { defineStore } from 'pinia'
import modalApi from '@/api/modal'

export const useAirtightnessImageQueryStore = defineStore('airtightnessImageQuery', {
  state: () => ({
    // 查询表单状态
    searchForm: {
      vehicleModelIds: []
    },

    // 选项数据
    vehicleModelOptions: [],
    
    // 加载状态
    vehicleModelsLoading: false,
    queryLoading: false,
    
    // 查询结果
    imageDataList: [],

    // UI状态
    selectAllVehicles: false
  }),
  
  getters: {
    // 是否有查询结果
    hasResults: (state) => {
      return state.imageDataList.length > 0
    },

    // 选中的车型数量
    selectedVehicleCount: (state) => {
      return state.searchForm.vehicleModelIds.length
    },

    // 全选状态
    isAllVehiclesSelected: (state) => {
      return state.vehicleModelOptions.length > 0 &&
             state.searchForm.vehicleModelIds.length === state.vehicleModelOptions.length
    }
  },
  
  actions: {
    // 加载车型列表
    async loadVehicleModels() {
      try {
        this.vehicleModelsLoading = true
        const response = await modalApi.getVehicleModels()
        this.vehicleModelOptions = response.data || []
      } catch (error) {
        console.error('加载车型列表失败:', error)
        throw error
      } finally {
        this.vehicleModelsLoading = false
      }
    },

    // 查询气密性图片数据
    async queryImageData() {
      try {
        this.queryLoading = true

        const params = {}

        // 如果选择了车型，添加到查询参数
        if (this.searchForm.vehicleModelIds.length > 0) {
          params.vehicle_model_ids = this.searchForm.vehicleModelIds.join(',')
        }

        const response = await modalApi.getAirtightnessImages(params)
        this.imageDataList = response.data || []

        return this.imageDataList
      } catch (error) {
        console.error('查询气密性图片数据失败:', error)
        throw error
      } finally {
        this.queryLoading = false
      }
    },
    
    // 设置车型选择
    setVehicleModels(vehicleIds) {
      this.searchForm.vehicleModelIds = vehicleIds
      this.updateSelectAllState()
    },
    
    // 全选/反选车型
    toggleSelectAllVehicles(checked) {
      if (checked) {
        this.searchForm.vehicleModelIds = this.vehicleModelOptions.map(v => v.id)
      } else {
        this.searchForm.vehicleModelIds = []
      }
      this.selectAllVehicles = checked
    },
    
    // 更新全选状态
    updateSelectAllState() {
      if (this.searchForm.vehicleModelIds.length === 0) {
        this.selectAllVehicles = false
      } else if (this.searchForm.vehicleModelIds.length === this.vehicleModelOptions.length) {
        this.selectAllVehicles = true
      } else {
        this.selectAllVehicles = false
      }
    },
    
    // 清空所有状态
    resetState() {
      this.searchForm = {
        vehicleModelIds: []
      }
      this.imageDataList = []
      this.selectAllVehicles = false
    },
    
    // 初始化页面数据
    async initializePageData() {
      if (this.vehicleModelOptions.length === 0) {
        await this.loadVehicleModels()
      }
    }
  }
})
