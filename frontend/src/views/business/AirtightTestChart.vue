<template>
  <div class="airtight-test-chart">
    <!-- 搜索卡片 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="store.searchForm" label-width="120px" class="search-form">
        <el-row :gutter="24">
          <!-- 车型选择 -->
          <el-col :span="8">
            <el-form-item label="车型" required>
              <el-select
                v-model="store.searchForm.vehicleModelId"
                placeholder="请选择车型"
                :loading="store.vehicleModelsLoading"
                @change="handleVehicleModelChange"
                style="width: 100%"
              >
                <el-option
                  v-for="vehicle in store.vehicleModelOptions"
                  :key="vehicle.id"
                  :label="vehicle.vehicle_model_name"
                  :value="vehicle.id"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 日期范围 -->
          <el-col :span="8">
            <el-form-item label="测试日期">
              <el-date-picker
                v-model="store.searchForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                @change="handleDateRangeChange"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <!-- 操作按钮 -->
          <el-col :span="8">
            <el-form-item>
              <el-button
                type="primary"
                :icon="Search"
                @click="handleQuery"
                :loading="store.queryLoading"
                :disabled="!store.canQuery"
              >
                查询
              </el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 图表展示卡片 -->
    <el-card class="chart-card" shadow="never" v-if="store.hasResults">
      <template #header>
        <div class="card-header">
          <el-icon><TrendCharts /></el-icon>
          <span>气密性测试数据图表</span>
        </div>
      </template>

      <!-- 图表容器 -->
      <div ref="chartRef" class="chart-container"></div>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never" v-if="store.hasResults">
      <template #header>
        <div class="card-header">
          <el-icon><List /></el-icon>
          <span>气密性测试数据详情</span>
        </div>
      </template>

      <div class="table-container">
        <div v-for="category in store.testDataList" :key="category.category" class="category-section">
          <h4 class="category-title">{{ category.category }}</h4>
          <el-table
            :data="category.items"
            stripe
            :header-cell-style="{ backgroundColor: '#fafafa', color: '#606266', fontWeight: '600' }"
          >
            <el-table-column prop="name" label="测试项目" width="200" />
            <el-table-column label="测试数值 (SCFM)" align="center">
              <template #default="scope">
                <span v-for="(value, index) in scope.row.values" :key="index" class="value-item">
                  {{ value }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>

    <!-- 空状态 -->
    <el-card class="result-card" shadow="never" v-else-if="!store.queryLoading">
      <el-empty description="请选择车型进行查询" />
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated, onDeactivated, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, TrendCharts, List } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { useAirtightTestChartStore } from '@/store'

// 组件名称，用于keep-alive缓存
defineOptions({
  name: 'AirtightTestChart'
})

// 使用Pinia store
const store = useAirtightTestChartStore()

// 图表相关
const chartRef = ref(null)

// 车型选择变化处理
const handleVehicleModelChange = (vehicleModelId) => {
  store.setVehicleModel(vehicleModelId)
}

// 日期范围变化处理
const handleDateRangeChange = (dateRange) => {
  store.setDateRange(dateRange)
}

// 查询处理
const handleQuery = async () => {
  if (!store.canQuery) {
    ElMessage.warning('请选择车型')
    return
  }

  try {
    const result = await store.queryTestData()

    if (result.length > 0) {
      ElMessage.success('查询成功')
      // 等待DOM更新后渲染图表
      await nextTick()
      renderChart()
    } else {
      ElMessage.warning('未找到匹配的测试数据')
    }
  } catch (error) {
    console.error('查询失败:', error)
    ElMessage.error('查询失败')
  }
}

// 重置表单
const handleReset = () => {
  store.resetState()

  if (store.chartInstance) {
    store.chartInstance.dispose()
    store.setChartInstance(null)
  }
}

// 渲染图表
const renderChart = () => {
  if (!chartRef.value || !store.hasResults) return

  // 清除之前的图表实例
  if (store.chartInstance) {
    store.chartInstance.dispose()
  }

  // 创建新的图表实例
  const chartInstance = echarts.init(chartRef.value)
  store.setChartInstance(chartInstance)

  // 准备图表数据
  const categories = []
  const seriesData = []

  store.testDataList.forEach(category => {
    category.items.forEach(item => {
      categories.push(item.name)
      // 取第一个数值作为图表数据（因为只选择了一个车型）
      const value = parseFloat(item.values[0]) || 0
      seriesData.push(value)
    })
  })

  // 图表配置
  const option = {
    title: {
      text: '气密性测试数据',
      left: 'center',
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#303133'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        const data = params[0]
        return `${data.name}<br/>${data.seriesName}: ${data.value} SCFM`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: categories,
      axisLabel: {
        rotate: 45,
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      name: '泄漏量 (SCFM)',
      nameTextStyle: {
        fontSize: 14,
        fontWeight: 'bold'
      }
    },
    series: [{
      name: '泄漏量',
      type: 'bar',
      data: seriesData,
      itemStyle: {
        color: '#409eff'
      }
    }]
  }

  chartInstance.setOption(option)

  // 响应式处理
  const resizeHandler = () => {
    if (store.chartInstance) {
      store.chartInstance.resize()
    }
  }

  window.removeEventListener('resize', resizeHandler)
  window.addEventListener('resize', resizeHandler)
}

// 生命周期
onMounted(async () => {
  console.log('AirtightTestChart mounted - 初始化页面数据')
  await store.initializePageData()
})

// keep-alive 激活时
onActivated(async () => {
  console.log('AirtightTestChart activated - 恢复组件状态')

  // 初始化页面数据
  await store.initializePageData()

  // 强制重新渲染图表（如果有数据）
  if (store.hasResults) {
    // 等待DOM完全更新
    await nextTick()

    // 确保图表容器存在
    if (chartRef.value) {
      console.log('重新渲染气密性测试图表，数据条数:', store.testDataList.length)

      // 清除之前的图表实例
      if (store.chartInstance) {
        store.chartInstance.dispose()
        store.setChartInstance(null)
      }

      // 重新渲染图表
      renderChart()
    } else {
      console.warn('图表容器不存在，延迟渲染')
      setTimeout(() => {
        if (chartRef.value) {
          renderChart()
        }
      }, 100)
    }
  }

  console.log('气密性测试图表组件状态恢复完成:', {
    vehicleModelId: store.searchForm.vehicleModelId,
    resultCount: store.testDataList.length,
    hasChartContainer: !!chartRef.value
  })
})

// keep-alive 停用时
onDeactivated(() => {
  console.log('AirtightTestChart deactivated - 保存组件状态')

  // 移除窗口resize监听器，避免内存泄漏
  if (store.chartInstance) {
    window.removeEventListener('resize', store.chartInstance.resize)
  }
})

<style scoped>
.airtight-test-chart {
  padding: 0;
  background-color: #f5f7fa;
}

/* 卡片样式 */
.search-card,
.chart-card,
.table-card,
.result-card {
  margin-bottom: 24px;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.search-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.search-card :deep(.el-form-item__label) {
  color: white;
  font-weight: 600;
}

.search-card :deep(.el-input__inner),
.search-card :deep(.el-select .el-input__inner) {
  background-color: rgba(255, 255, 255, 0.9);
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.card-header .el-icon {
  font-size: 18px;
  color: #409eff;
}

/* 图表容器 */
.chart-container {
  width: 100%;
  height: 400px;
  margin: 20px 0;
}

/* 表格容器 */
.table-container {
  margin-top: 20px;
}

.category-section {
  margin-bottom: 30px;
}

.category-title {
  margin: 0 0 16px 0;
  padding: 8px 16px;
  background-color: #f0f9ff;
  border-left: 4px solid #409eff;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.value-item {
  display: inline-block;
  margin-right: 12px;
  padding: 4px 8px;
  background-color: #f0f9ff;
  border-radius: 4px;
  color: #409eff;
  font-weight: 600;
}

/* 搜索表单 */
.search-form {
  margin: 0;
}

.search-form :deep(.el-form-item) {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-container {
    height: 300px;
  }

  .category-title {
    font-size: 14px;
  }
}
</style>
